# IF 設計書調整ルール

## システム構成とデータフロー

```
MDM/OMS（上流） → デ連（中流） → DWH（下流）
```

**基本原則**: 上流システム（MDM/OMS）の項目仕様を基準として、下流システム（デ連・DWH）の IF 設計書を調整する

## 対応内容別調整ルール

### 1. 項目の存在に関する調整

#### 項目追加が必要

- **状況**: MDM/OMS に項目が存在するが、デ連・DWH の IF 設計書に項目が存在しない
- **対応**:
  - デ連 IF 設計書に項目を追加
  - DWHIF 設計書に項目を追加
- **影響範囲**: デ連、DWH 両方

#### 項目削除が必要

- **状況**: MDM/OMS に項目が存在しないが、デ連・DWH の IF 設計書に項目が存在する
- **対応**:
  - デ連 IF 設計書から項目を削除
  - DWHIF 設計書から項目を削除
- **影響範囲**: デ連、DWH 両方

### 2. 必須属性に関する調整

#### 必須追加が必要 / 必須設定が必要

- **状況**: MDM/OMS で必須項目だが、下流システムで必須設定されていない
- **対応**:
  - デ連 IF 設計書で必須属性を設定
  - DWHIF 設計書で必須属性を設定
- **影響範囲**: デ連、DWH 両方

#### デ連 IF のみ必須解除が必要

- **状況**: MDM/OMS で必須でないが、デ連 IF 設計書で必須設定されている
- **対応**:
  - デ連 IF 設計書の必須属性を解除
- **影響範囲**: デ連のみ

### 3. データ仕様に関する調整

#### 桁数修正が必要

- **状況**: MDM/OMS と下流システムで項目の桁数仕様が異なる
- **対応**:
  - デ連 IF 設計書の桁数を MDM/OMS に合わせて修正
  - DWHIF 設計書の桁数を MDM/OMS に合わせて修正
- **影響範囲**: デ連、DWH 両方

#### デ連 IF のみ桁数修正が必要

- **状況**: MDM/OMS とデ連 IF 設計書で項目の桁数仕様が異なる
- **対応**:
  - デ連 IF 設計書の桁数を MDM/OMS に合わせて修正
- **影響範囲**: デ連のみ

### 4. 制約に関する調整

#### デ連 IF のみユニーク解除が必要

- **状況**: MDM/OMS でユニーク制約がないが、デ連 IF 設計書でユニーク制約が設定されている
- **対応**:
  - デ連 IF 設計書のユニーク制約を解除
- **影響範囲**: デ連のみ

## 調整パターンの分類

### 全システム対象の調整

- 項目追加が必要
- 項目削除が必要
- 必須追加が必要 / 必須設定が必要
- 桁数修正が必要

### デ連のみ対象の調整

- デ連 IF のみ必須解除が必要
- デ連 IF のみ桁数修正が必要
- デ連 IF のみユニーク解除が必要

## 作業手順

1. **MDM/OMS 仕様の確認**: 基準となる上流システムの項目仕様を確認
2. **現状の IF 設計書確認**: デ連・DWH の現在の IF 設計書を確認
3. **差分の特定**: 上流と下流の仕様差分を特定
4. **調整方針の決定**: 上記ルールに基づいて調整方針を決定
5. **IF 設計書の修正**:
   - デ連 IF 設計書の修正
   - DWHIF 設計書の修正
6. **変更履歴の追加**: IF 設計書の変更履歴シートに変更内容を記録
7. **影響範囲の確認**: 修正による他システムへの影響を確認

## 変更履歴シート記録ルール

### 変更履歴の記録形式

| No. | 版   | 日付       | 区分 | 変更前 | 変更後 | 変更内容                                                                                                                                             | 担当者 |
| --- | ---- | ---------- | ---- | ------ | ------ | ---------------------------------------------------------------------------------------------------------------------------------------------------- | ------ |
| 4   | 1.01 | 2025/07/09 | 変更 | -      | -      | "#8254 対応<br>断面差異対応(IF)<br> ・項目削除：クーポン商品シリーズ/クーポン大分類/クーポン部門/販売経路指定<br> ・必須に設定:クーポン自動発行種別" | TIS 黄 |

### 変更内容の記載ルール

#### 項目追加の場合

```
#8254対応
断面差異対応(IF)
 ・項目追加：[項目名1]/[項目名2]/[項目名3]
```

#### 項目削除の場合

```
#8254対応
断面差異対応(IF)
 ・項目削除：[項目名1]/[項目名2]/[項目名3]
```

#### 必須設定の場合

```
#8254対応
断面差異対応(IF)
 ・必須に設定：[項目名1]/[項目名2]/[項目名3]
```

#### 必須解除の場合

```
#8254対応
断面差異対応(IF)
 ・必須を解除：[項目名1]/[項目名2]/[項目名3]
```

#### 桁数修正の場合

```
#8254対応
断面差異対応(IF)
 ・桁数修正：[項目名1]([変更前桁数]→[変更後桁数])/[項目名2]([変更前桁数]→[変更後桁数])
```

#### ユニーク制約解除の場合

```
#8254対応
断面差異対応(IF)
 ・ユニーク制約解除：[項目名1]/[項目名2]
```

#### 複数種類の変更がある場合

```
#8254対応
断面差異対応(IF)
 ・項目追加：[項目名1]/[項目名2]
 ・項目削除：[項目名3]/[項目名4]
 ・必須に設定：[項目名5]
 ・桁数修正：[項目名6]([変更前桁数]→[変更後桁数])
```

### 版番号の管理ルール

- **メジャーバージョン**: 大幅な仕様変更時（例：1.00 → 2.00）
- **マイナーバージョン**: 項目の追加・削除・属性変更時（例：1.00 → 1.01）
- **パッチバージョン**: 軽微な修正時（例：1.01 → 1.02）

### 変更履歴追加時の注意事項

1. **No.の連番**: 既存の最大 No.に+1 して設定
2. **版の更新**: 変更内容に応じて適切に版番号を更新
3. **日付**: 変更作業実施日を記録
4. **区分**: 通常は「変更」を設定
5. **変更前/変更後**: 項目レベルの変更では通常「-」を設定
6. **変更内容**: 上記ルールに従って統一形式で記載
7. **担当者**: 実際の作業担当者名を記録

## 注意事項

- **上流基準の原則**: 常に MDM/OMS の仕様を基準とする
- **データ整合性**: 修正により上流から下流へのデータフローが正常に動作することを確認
- **既存データへの影響**: 既存データに対する影響を事前に評価
- **段階的な適用**: 必要に応じて段階的に IF 設計書を修正
- **テスト計画**: 修正後のテスト計画を事前に策定

## 対応優先度

1. **高優先度**: 必須属性の追加・削除（データ整合性に直接影響）
2. **中優先度**: 項目の追加・削除（機能に影響）
3. **低優先度**: 桁数修正、制約の調整（運用に影響）

すべての調整は担当者「黄」により実施される。
